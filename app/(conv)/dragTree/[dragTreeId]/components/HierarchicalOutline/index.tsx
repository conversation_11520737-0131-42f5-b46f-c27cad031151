'use client'

import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  lazy,
  Suspense,
} from 'react'
import OutlineView from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/OutlineView'
import VirtualizedOutline from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/VirtualizedOutline'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { useBatchResearch } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useBatchResearch'
import { ChevronsDown, ChevronsUp, FlaskConical, Archive } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { isLocalOrDevEnv } from '@/lib/utils'
import { useAssetStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import AssetSidebar from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/AssetSidebar'

// Helper function moved outside component - pure utility function
const findNodePath = (
  node: any,
  targetId: string,
  path: string[] = []
): string[] | null => {
  if (node.id === targetId) {
    return path
  }

  for (const child of node.children) {
    const foundPath = findNodePath(child, targetId, [...path, node.id])
    if (foundPath) {
      return foundPath
    }
  }

  return null
}

type HierarchicalOutlineProps = {
  onGenerateSimilarQuestions?: (nodeId: string, guidance: string) => void
  onGenerateSimilarCategories?: (nodeId: string, guidance: string) => void
  onMarkAsInterested?: (nodeId: string) => void
  isLoading?: boolean
  targetNodeId?: string
  dragTreeId?: string
}

const BatchResearchDialog = lazy(
  () =>
    import(
      '@/app/(conv)/dragTree/[dragTreeId]/components/shared/BatchResearchDialog'
    )
)

const HierarchicalOutline: React.FC<HierarchicalOutlineProps> = ({
  onGenerateSimilarQuestions,
  onGenerateSimilarCategories,
  // onMarkAsInterested is unused
  isLoading = false,
  targetNodeId = '',
  dragTreeId = '',
}) => {
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const addNode = useDragTreeStore(state => state.addNode)
  const deleteNode = useDragTreeStore(state => state.deleteNode)
  const editNode = useDragTreeStore(state => state.editNode)
  const reorderNode = useDragTreeStore(state => state.reorderNode)

  const { treeTargetNodeId } = useNavigationStore()

  // Initialize batch research hook
  const batchResearch = useBatchResearch(dragTreeId)

  // Asset management - Subscribe to store to get reactive updates
  const { isAssetSidebarOpen, toggleAssetSidebar, closeAssetSidebar, assets } =
    useAssetStore()

  // Calculate unviewed count reactively from subscribed store state
  const currentDragTreeId = dragTreeId || 'current'
  const currentTreeAssets = assets.filter(
    asset =>
      asset.dragTreeId === currentDragTreeId || asset.dragTreeId === 'current'
  )

  const unviewedAssetCount = currentTreeAssets.filter(
    asset => !asset.viewed
  ).length

  const [editingNode, setEditingNode] = useState<string | null>(null)
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set())
  // Track whether we have already applied the initial auto-collapse logic.
  const hasInitializedCollapse = useRef<boolean>(false)

  // Check if we're in local or development environment to show batch research feature
  const showBatchResearch = isLocalOrDevEnv()

  // Memoized function to get categories with questions - prevents duplicate calculations
  const categoriesWithQuestions = useMemo(() => {
    if (!frontendTreeStructure) return []

    const getCategoriesWithQuestions = (node: any): string[] => {
      const ids: string[] = []
      if (node.type === 'category' && node.children.length > 0) {
        const hasQuestions = node.children.some(
          (child: any) => child.type === 'question'
        )
        if (hasQuestions) {
          ids.push(node.id)
        }
      }
      if (node.children) {
        node.children.forEach((child: any) => {
          ids.push(...getCategoriesWithQuestions(child))
        })
      }
      return ids
    }

    return getCategoriesWithQuestions(frontendTreeStructure)
  }, [frontendTreeStructure])

  // Initialize collapsed nodes when tree structure loads
  useEffect(() => {
    // Only run this auto-collapse logic once (on first load) or when the underlying
    // tree structure actually changes (e.g. after loading a brand-new drag tree).
    if (!frontendTreeStructure || hasInitializedCollapse.current) return

    const initialCollapsed = new Set<string>()

    const shouldCollapseByDefault = (node: any): boolean => {
      if (node.type !== 'category') return false
      if (node.children.length === 0) return false

      // Check if all children are question nodes (no child categories)
      const hasChildCategories = node.children.some(
        (child: any) => child.type === 'category'
      )

      // Collapse categories that only have question children
      return !hasChildCategories
    }

    const collectCollapsibleNodes = (node: any) => {
      if (shouldCollapseByDefault(node)) {
        initialCollapsed.add(node.id)
      }
      node.children.forEach(collectCollapsibleNodes)
    }

    collectCollapsibleNodes(frontendTreeStructure)

    if (initialCollapsed.size > 0) {
      console.log(
        `🌳 [HierarchicalOutline] Auto-collapsing ${initialCollapsed.size} categories with only questions:`,
        Array.from(initialCollapsed)
      )
      setCollapsedNodes(initialCollapsed)
      // Mark that we have executed the initial collapse so it does not run again
      hasInitializedCollapse.current = true
    }
  }, [frontendTreeStructure])

  // Handle navigation from React Flow to tree node
  // Auto-expand parent categories and scroll to target node
  useEffect(() => {
    if (!treeTargetNodeId || !frontendTreeStructure) return

    console.log(
      '🧭 [HierarchicalOutline] Navigating to node:',
      treeTargetNodeId
    )

    // Find the path to the target node (excluding the target itself)
    const pathToTarget = findNodePath(frontendTreeStructure, treeTargetNodeId)

    if (pathToTarget) {
      // Expand all parent categories in the path
      const newCollapsedNodes = new Set(collapsedNodes)
      let hasChanges = false

      pathToTarget.forEach(nodeId => {
        if (newCollapsedNodes.has(nodeId)) {
          newCollapsedNodes.delete(nodeId)
          hasChanges = true
          console.log(
            `🔓 [HierarchicalOutline] Expanding parent category: ${nodeId}`
          )
        }
      })

      if (hasChanges) {
        setCollapsedNodes(newCollapsedNodes)
        console.log(
          `🌳 [HierarchicalOutline] Expanded ${pathToTarget.length} parent categories for question visibility`
        )
      }
    }

    const scrollToNode = () => {
      const targetElement = document.querySelector(
        `[data-node-id="${treeTargetNodeId}"]`
      ) as HTMLElement

      if (!targetElement) {
        console.warn(
          '⚠️ [HierarchicalOutline] Target node element not found:',
          treeTargetNodeId
        )
        return
      }

      // Find the scrollable container (the overflow-auto div)
      const scrollContainer = targetElement.closest('.overflow-auto')
      if (!scrollContainer) {
        console.warn('⚠️ [HierarchicalOutline] Scroll container not found')
        return
      }

      const elementRect = targetElement.getBoundingClientRect()
      const containerRect = scrollContainer.getBoundingClientRect()

      // Calculate scroll position to center the element
      const scrollTop =
        scrollContainer.scrollTop +
        elementRect.top -
        containerRect.top -
        containerRect.height / 2 +
        elementRect.height / 2

      scrollContainer.scrollTo({
        top: scrollTop,
        behavior: 'smooth',
      })

      console.log(
        '📍 [HierarchicalOutline] Scrolled to node:',
        treeTargetNodeId
      )

      // Find the Card parent element for better highlighting
      const cardElement = targetElement.closest('.mb-4') as HTMLElement
      const elementToHighlight = cardElement || targetElement

      // Add highlight effect
      elementToHighlight.style.setProperty(
        'background-color',
        'rgba(16, 185, 129, 0.4)',
        'important'
      )
      elementToHighlight.style.setProperty(
        'border',
        '2px solid rgba(16, 185, 129, 0.6)',
        'important'
      )
      elementToHighlight.style.transition = 'all 0.3s ease-in-out'

      // Remove highlight after 2 seconds
      setTimeout(() => {
        elementToHighlight.style.removeProperty('background-color')
        elementToHighlight.style.removeProperty('border')
        elementToHighlight.style.transition = ''
      }, 2000)
    }

    // Use a longer delay to ensure DOM updates from expansion complete
    const timeoutId = setTimeout(scrollToNode, 300)
    return () => clearTimeout(timeoutId) // Cleanup timeout on unmount
  }, [
    treeTargetNodeId,
    frontendTreeStructure,
    collapsedNodes,
    setCollapsedNodes,
  ])

  /* ------------------------------------------------------------
   * Virtualization decision – count total nodes once and decide
   * ---------------------------------------------------------- */
  const totalNodeCount = useMemo(() => {
    if (!frontendTreeStructure) return 0
    const countNodes = (node: any): number => {
      return (
        1 +
        node.children.reduce(
          (acc: number, child: any) => acc + countNodes(child),
          0
        )
      )
    }
    return countNodes(frontendTreeStructure)
  }, [frontendTreeStructure])

  // Temporarily disable virtualization for typical trees (fixes card overlap & navigation)
  const ENABLE_VIRTUALIZATION_THRESHOLD = 500
  const useVirtualList = totalNodeCount > ENABLE_VIRTUALIZATION_THRESHOLD

  if (!frontendTreeStructure) {
    return (
      <div className="p-8 text-center text-gray-500">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <p className="mt-4">Loading tree data...</p>
      </div>
    )
  }

  // Smart toggle functionality: expand all or collapse questions under categories
  const handleToggleExpansion = () => {
    if (!frontendTreeStructure || categoriesWithQuestions.length === 0) return

    // Check if any categories with questions are currently collapsed
    const hasCollapsedCategories = categoriesWithQuestions.some(id =>
      collapsedNodes.has(id)
    )

    if (hasCollapsedCategories) {
      // Expand all - show all questions
      const newCollapsedNodes = new Set<string>() // Empty set = all expanded
      setCollapsedNodes(newCollapsedNodes)
      console.log(
        `🌳 [HierarchicalOutline] Expanded all ${categoriesWithQuestions.length} categories with questions`
      )
    } else {
      // Collapse questions under categories (but keep category structure visible)
      const newCollapsedNodes = new Set(categoriesWithQuestions)
      setCollapsedNodes(newCollapsedNodes)
      console.log(
        `🌳 [HierarchicalOutline] Collapsed questions under ${categoriesWithQuestions.length} categories`
      )
    }
  }

  // Determine current state for button text using memoized categories
  const hasCollapsedCategories = categoriesWithQuestions.some(id =>
    collapsedNodes.has(id)
  )
  const isExpanded = !hasCollapsedCategories

  return (
    <div id="tutorial-outline-panel" className="h-full flex flex-col">
      {/* Compact Header with Controls */}
      <div className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-1 border-b bg-white/50 backdrop-blur-sm">
        {/* Expand/Collapse button */}
        <Button
          variant={isExpanded ? 'ghost' : 'default'}
          size="sm"
          onClick={handleToggleExpansion}
          className={cn(
            'px-2 sm:px-2.5 py-1.5 sm:py-1 text-xs font-medium border-b-2 transition-all duration-200 cursor-pointer flex-shrink-0 hover:bg-slate-50 whitespace-nowrap touch-manipulation',
            isExpanded
              ? 'border-transparent bg-white text-gray-700 hover:text-gray-900'
              : 'border-blue-500 bg-white text-blue-700'
          )}
          title={
            isExpanded
              ? 'Collapse questions under categories'
              : 'Expand all categories to show questions'
          }
        >
          {isExpanded ? (
            <ChevronsUp className="w-3.5 h-3.5 sm:w-3 sm:h-3 mr-1 sm:mr-1.5" />
          ) : (
            <ChevronsDown className="w-3.5 h-3.5 sm:w-3 sm:h-3 mr-1 sm:mr-1.5" />
          )}
          <span className="truncate flex-1">
            {isExpanded ? 'Collapse' : 'Expand All'}
          </span>
        </Button>

        {/* Batch Research button - shown only in development */}
        {showBatchResearch && (
          <Button
            variant="default"
            size="sm"
            onClick={batchResearch.openBatchResearch}
            disabled={batchResearch.isProcessing || !frontendTreeStructure}
            className="px-2 sm:px-2.5 py-1.5 sm:py-1 text-xs font-medium border-b-2 transition-all duration-200 cursor-pointer flex-shrink-0 hover:bg-slate-50 whitespace-nowrap border-purple-500 bg-white text-purple-700 touch-manipulation"
            title="Start batch research for multiple questions"
          >
            <FlaskConical className="w-3.5 h-3.5 sm:w-3 sm:h-3 mr-1 sm:mr-1.5" />
            <span className="truncate flex-1">Batch Quick Research</span>
          </Button>
        )}

        {/* Assets button – always visible for better discoverability */}
        <Button
          variant="default"
          size="sm"
          onClick={toggleAssetSidebar}
          className="px-2 sm:px-2.5 py-1.5 sm:py-1 text-xs font-medium border-b-2 transition-all duration-200 cursor-pointer flex-shrink-0 hover:bg-slate-50 whitespace-nowrap border-orange-500 bg-white text-orange-700 relative touch-manipulation"
          title="View generated assets"
        >
          <Archive className="w-3.5 h-3.5 sm:w-3 sm:h-3 mr-1 sm:mr-1.5" />
          <span className="truncate flex-1">Assets</span>
          {unviewedAssetCount > 0 && (
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center animate-pulse border-2 border-white shadow-lg z-10">
              <span className="text-white text-xs font-bold leading-none">
                {unviewedAssetCount > 9 ? '9+' : unviewedAssetCount}
              </span>
            </div>
          )}
        </Button>

        {/* Spacer to push future controls to the right if needed */}
        <div className="flex-1" />
      </div>

      {/* Main tree view */}
      <div className="flex-1 overflow-auto scroll-smooth pb-8 pt-2 px-2 sm:px-0">
        {useVirtualList ? (
          <VirtualizedOutline
            root={frontendTreeStructure}
            collapsedNodes={collapsedNodes}
            setCollapsedNodes={setCollapsedNodes}
            onAdd={addNode}
            onDelete={deleteNode}
            onEdit={editNode}
            onReorder={reorderNode}
            editingNode={editingNode}
            setEditingNode={setEditingNode}
            targetNodeId={targetNodeId}
            onGenerateSimilarQuestions={onGenerateSimilarQuestions}
            onGenerateSimilarCategories={onGenerateSimilarCategories}
            isLoading={isLoading}
            loadingNodeId={targetNodeId}
            isGloballyExpanded={isExpanded}
          />
        ) : (
          <OutlineView
            node={frontendTreeStructure}
            onAdd={addNode}
            onDelete={deleteNode}
            onEdit={editNode}
            onReorder={reorderNode}
            editingNode={editingNode}
            setEditingNode={setEditingNode}
            collapsedNodes={collapsedNodes}
            setCollapsedNodes={setCollapsedNodes}
            targetNodeId={targetNodeId}
            onGenerateSimilarQuestions={onGenerateSimilarQuestions}
            onGenerateSimilarCategories={onGenerateSimilarCategories}
            isLoading={isLoading}
            loadingNodeId={targetNodeId}
            isGloballyExpanded={isExpanded}
          />
        )}
      </div>

      {/* Batch Research Dialog */}
      <Suspense
        fallback={
          <div className="animate-pulse bg-gray-100 rounded-lg h-32 w-full" />
        }
      >
        <BatchResearchDialog
          isOpen={batchResearch.isOpen}
          onClose={batchResearch.closeBatchResearch}
          dragTreeId={dragTreeId || targetNodeId} // Use dragTreeId prop or fallback to targetNodeId
          items={batchResearch.items}
          categoryGroups={batchResearch.categoryGroups}
          hierarchicalStructure={batchResearch.hierarchicalStructure}
          selectedItems={batchResearch.selectedItems}
          onToggleItem={batchResearch.toggleItemSelection}
          onToggleCategorySelection={batchResearch.toggleCategorySelection}
          onStartBatch={batchResearch.startBatchResearch}
          onCancelBatch={batchResearch.cancelBatchResearch}
          isProcessing={batchResearch.isProcessing}
          processedCount={batchResearch.processedCount}
          totalCount={batchResearch.totalCount}
          currentProcessingId={batchResearch.currentProcessingId}
          // Coach mode props
          isCoachMode={batchResearch.isCoachMode}
          currentQuestionIndex={batchResearch.currentQuestionIndex}
          currentQuestion={batchResearch.currentQuestion}
          coachModeQuestions={batchResearch.coachModeQuestions}
          coachModeTotal={batchResearch.coachModeTotal}
          onToggleCoachMode={batchResearch.toggleCoachMode}
          onGoToNextQuestion={batchResearch.goToNextQuestion}
          onGoToPreviousQuestion={batchResearch.goToPreviousQuestion}
          onSelectCurrentQuestion={batchResearch.selectCurrentQuestion}
          onSkipCurrentQuestion={batchResearch.skipCurrentQuestion}
        />
      </Suspense>

      {/* Asset Sidebar */}
      <AssetSidebar
        dragTreeId={dragTreeId || 'current'}
        isOpen={isAssetSidebarOpen}
        onClose={closeAssetSidebar}
      />
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(HierarchicalOutline)
